import { NotificationType } from "@prisma/client";
import { prisma } from "../config/database";
import { sendRealTimeNotification } from "./websocket";
import { sendEmail } from "./emailService";

interface CreateNotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
}

/**
 * Create and send a notification
 */
export async function createNotification(notificationData: CreateNotificationData) {
  try {
    // Create notification in database
    const notification = await prisma.notification.create({
      data: notificationData,
    });

    // Get user's notification preferences
    const preferences = await prisma.notificationPreferences.findUnique({
      where: { userId: notificationData.userId },
    });

    // Send real-time notification if user is online
    sendRealTimeNotification(notificationData.userId, {
      id: notification.id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      data: notification.data,
      createdAt: notification.createdAt,
      isRead: notification.isRead,
    });

    // Send email notification if enabled
    if (preferences && shouldSendEmailNotification(notificationData.type, preferences)) {
      const user = await prisma.user.findUnique({
        where: { id: notificationData.userId },
        select: { email: true, name: true },
      });

      if (user) {
        await sendEmailNotification(user.email, user.name, notification);
      }
    }

    return notification;
  } catch (error) {
    console.error("Error creating notification:", error);
    throw error;
  }
}

/**
 * Create notifications for multiple users
 */
export async function createBulkNotifications(notifications: CreateNotificationData[]) {
  try {
    // Create all notifications in database
    const createdNotifications = await prisma.notification.createMany({
      data: notifications,
    });

    // Send real-time notifications for online users
    for (const notificationData of notifications) {
      sendRealTimeNotification(notificationData.userId, {
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        data: notificationData.data,
        createdAt: new Date(),
        isRead: false,
      });
    }

    // TODO: Handle bulk email notifications
    // This could be optimized to batch email sends

    return createdNotifications;
  } catch (error) {
    console.error("Error creating bulk notifications:", error);
    throw error;
  }
}

/**
 * Check if email notification should be sent based on user preferences
 */
function shouldSendEmailNotification(type: NotificationType, preferences: any): boolean {
  switch (type) {
    case "MESSAGE_RECEIVED":
      return preferences.emailMessageReceived;
    case "CONNECTION_REQUEST":
      return preferences.emailConnectionRequest;
    case "POST_CREATED":
      return preferences.emailPostCreated;
    case "SYSTEM":
      return preferences.emailSystemUpdates;
    default:
      return false;
  }
}

/**
 * Send email notification
 */
async function sendEmailNotification(email: string, name: string, notification: any) {
  try {
    const subject = `Alumni Portal: ${notification.title}`;
    const html = generateEmailTemplate(name, notification);

    await sendEmail({
      to: email,
      subject,
      html,
    });
  } catch (error) {
    console.error("Error sending email notification:", error);
    // Don't throw error - email failure shouldn't break notification creation
  }
}

/**
 * Generate email template for notification
 */
function generateEmailTemplate(name: string, notification: any): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${notification.title}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Alumni Portal</h1>
        </div>
        <div class="content">
          <h2>Hi ${name},</h2>
          <h3>${notification.title}</h3>
          <p>${notification.message}</p>
          <p>
            <a href="${process.env.FRONTEND_URL}/notifications" class="button">
              View in Alumni Portal
            </a>
          </p>
        </div>
        <div class="footer">
          <p>You received this email because you have email notifications enabled.</p>
          <p>
            <a href="${process.env.FRONTEND_URL}/settings/notifications">
              Manage notification preferences
            </a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

/**
 * Notification helper functions for different types
 */
export const NotificationHelpers = {
  async connectionRequest(receiverId: string, requesterName: string, connectionId: string) {
    await createNotification({
      userId: receiverId,
      type: "CONNECTION_REQUEST",
      title: "New Connection Request",
      message: `${requesterName} sent you a connection request`,
      data: { connectionId, requesterName },
    });
  },

  async messageReceived(receiverId: string, senderName: string, messageId: string) {
    await createNotification({
      userId: receiverId,
      type: "MESSAGE_RECEIVED",
      title: "New Message",
      message: `${senderName} sent you a message`,
      data: { messageId, senderName },
    });
  },

  async postCreated(postId: string, postTitle: string, authorName: string, connectedUserIds: string[]) {
    const notifications = connectedUserIds.map((userId) => ({
      userId,
      type: "POST_CREATED" as NotificationType,
      title: "New Post",
      message: `${authorName} shared a new post: ${postTitle}`,
      data: { postId, authorName },
    }));

    if (notifications.length > 0) {
      await createBulkNotifications(notifications);
    }
  },
};
