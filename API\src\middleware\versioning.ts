import { Request, Response, NextFunction } from "express";
import { Logger } from "../services/loggerService";

// API version configuration
const API_VERSIONS = {
  v1: "1.0.0",
  v2: "2.0.0",
} as const;

type ApiVersion = keyof typeof API_VERSIONS;

// Default version
const DEFAULT_VERSION: ApiVersion = "v1";

// Version compatibility matrix
const VERSION_COMPATIBILITY = {
  v1: ["v1"],
  v2: ["v2", "v1"], // v2 can fallback to v1
} as const;

// Extended Request interface with version info
declare global {
  namespace Express {
    interface Request {
      apiVersion?: ApiVersion;
      requestedVersion?: string | undefined;
    }
  }
}

// API versioning middleware
export const apiVersioning = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    let requestedVersion: string | undefined;
    let apiVersion: ApiVersion = DEFAULT_VERSION;

    // Check version from header (preferred method)
    const headerVersion = req.get("API-Version") || req.get("X-API-Version");
    if (headerVersion) {
      requestedVersion = headerVersion;
    }

    // Check version from Accept header
    const acceptHeader = req.get("Accept");
    if (acceptHeader && !requestedVersion) {
      const versionMatch = acceptHeader.match(/application\/vnd\.alumni-portal\.([^+]+)/);
      if (versionMatch) {
        requestedVersion = versionMatch[1];
      }
    }

    // Check version from URL path (e.g., /api/v1/users)
    const pathMatch = req.path.match(/^\/api\/(v\d+)\//);
    if (pathMatch && !requestedVersion) {
      requestedVersion = pathMatch[1];
    }

    // Check version from query parameter (fallback)
    if (req.query.version && !requestedVersion) {
      requestedVersion = String(req.query.version);
    }

    // Validate and set API version
    if (requestedVersion) {
      const normalizedVersion = requestedVersion.toLowerCase() as ApiVersion;
      if (API_VERSIONS[normalizedVersion]) {
        apiVersion = normalizedVersion;
      } else {
        // Try to find compatible version
        const compatibleVersion = findCompatibleVersion(requestedVersion);
        if (compatibleVersion) {
          apiVersion = compatibleVersion;
          Logger.warn(`API version ${requestedVersion} not found, using compatible version ${apiVersion}`, {
            requestedVersion,
            compatibleVersion: apiVersion,
            path: req.path,
            userAgent: req.get("User-Agent"),
          });
        } else {
          // Unsupported version
          return res.status(400).json({
            error: "Unsupported API version",
            requestedVersion,
            supportedVersions: Object.keys(API_VERSIONS),
            message: `API version '${requestedVersion}' is not supported. Please use one of: ${Object.keys(
              API_VERSIONS
            ).join(", ")}`,
          });
        }
      }
    }

    // Set version info on request
    req.apiVersion = apiVersion;
    req.requestedVersion = requestedVersion;

    // Add version headers to response
    res.set("API-Version", API_VERSIONS[apiVersion]);
    res.set("X-API-Version", apiVersion);
    res.set("X-Supported-Versions", Object.keys(API_VERSIONS).join(", "));

    // Log version usage for analytics
    Logger.api(`API version ${apiVersion} used`, req.path, req.method, {
      version: apiVersion,
      requestedVersion,
      userAgent: req.get("User-Agent"),
    });

    return next();
  };
};

// Find compatible version
function findCompatibleVersion(requestedVersion: string): ApiVersion | null {
  const normalizedRequested = requestedVersion.toLowerCase();

  // Direct match
  if (API_VERSIONS[normalizedRequested as ApiVersion]) {
    return normalizedRequested as ApiVersion;
  }

  // Try to match major version (e.g., "2" -> "v2")
  if (/^\d+$/.test(normalizedRequested)) {
    const versionKey = `v${normalizedRequested}` as ApiVersion;
    if (API_VERSIONS[versionKey]) {
      return versionKey;
    }
  }

  // Try to match semantic version (e.g., "2.1.0" -> "v2")
  const semverMatch = normalizedRequested.match(/^(\d+)\./);
  if (semverMatch) {
    const majorVersion = `v${semverMatch[1]}` as ApiVersion;
    if (API_VERSIONS[majorVersion]) {
      return majorVersion;
    }
  }

  return null;
}

// Version-specific route handler
export const versionedRoute = (handlers: Partial<Record<ApiVersion, any>>) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const version = req.apiVersion || DEFAULT_VERSION;
    let handler = handlers[version];

    if (!handler) {
      // Try to find a compatible handler
      const compatibleVersions = VERSION_COMPATIBILITY[version] || [version];
      let foundHandler = null;

      for (const compatibleVersion of compatibleVersions) {
        if (handlers[compatibleVersion]) {
          foundHandler = handlers[compatibleVersion];
          break;
        }
      }

      if (!foundHandler) {
        return res.status(501).json({
          error: "Version not implemented",
          version,
          message: `This endpoint is not implemented for API version ${version}`,
          availableVersions: Object.keys(handlers),
        });
      }

      handler = foundHandler;
    }

    // Call the version-specific handler
    if (typeof handler === "function") {
      return handler(req, res, next);
    } else {
      return next();
    }
  };
};

// Deprecation warning middleware
export const deprecationWarning = (version: ApiVersion, deprecatedIn: string, removedIn: string, message?: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.apiVersion === version) {
      const warningMessage =
        message || `API version ${version} is deprecated and will be removed in version ${removedIn}`;

      res.set("Warning", `299 - "Deprecated API" "${warningMessage}"`);
      res.set("Deprecation", "true");
      res.set("Sunset", removedIn);

      Logger.warn(`Deprecated API version used: ${version}`, {
        path: req.path,
        method: req.method,
        userAgent: req.get("User-Agent"),
        deprecatedIn,
        removedIn,
      });
    }

    next();
  };
};

// Version migration helper
export const versionMigration = (fromVersion: ApiVersion, toVersion: ApiVersion, migrationFn: (data: any) => any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.apiVersion === fromVersion) {
      // Store original json method
      const originalJson = res.json.bind(res);

      // Override json method to apply migration
      res.json = function (data: any) {
        try {
          const migratedData = migrationFn(data);
          return originalJson(migratedData);
        } catch (error) {
          Logger.error(`Version migration failed from ${fromVersion} to ${toVersion}`, {
            error: error instanceof Error ? error.message : String(error),
            path: req.path,
          });
          return originalJson(data); // Fallback to original data
        }
      };
    }

    next();
  };
};

// Content negotiation based on version
export const contentNegotiation = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const version = req.apiVersion || DEFAULT_VERSION;

    // Set content type based on version
    const contentType =
      version === "v2" ? "application/vnd.alumni-portal.v2+json" : "application/vnd.alumni-portal.v1+json";

    res.set("Content-Type", contentType);

    next();
  };
};

// Version analytics middleware
export const versionAnalytics = () => {
  const versionUsage = new Map<string, number>();

  // Log version usage every hour
  setInterval(() => {
    if (versionUsage.size > 0) {
      Logger.info("[ANALYTICS] API version usage", Object.fromEntries(versionUsage));
      versionUsage.clear();
    }
  }, 3600000); // 1 hour

  return (req: Request, _res: Response, next: NextFunction) => {
    const version = req.apiVersion || DEFAULT_VERSION;
    const key = `${version}_${req.method}_${req.route?.path || req.path}`;

    versionUsage.set(key, (versionUsage.get(key) || 0) + 1);

    next();
  };
};

// Get version information
export const getVersionInfo = () => {
  return {
    versions: API_VERSIONS,
    defaultVersion: DEFAULT_VERSION,
    compatibility: VERSION_COMPATIBILITY,
    supportedVersions: Object.keys(API_VERSIONS),
  };
};
