import nodemailer from "nodemailer";

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

// Create transporter
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || "587"),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

/**
 * Send email
 */
export async function sendEmail(options: EmailOptions): Promise<void> {
  try {
    // Skip sending emails in development if SMTP is not configured
    if (process.env.NODE_ENV === "development" && !process.env.SMTP_USER) {
      console.log("📧 Email would be sent:", {
        to: options.to,
        subject: options.subject,
        preview: options.html.substring(0, 100) + "...",
      });
      return;
    }

    const mailOptions = {
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text || stripHtml(options.html),
    };

    const info = await transporter.sendMail(mailOptions);
    console.log("📧 Email sent:", info.messageId);
  } catch (error) {
    console.error("❌ Email sending failed:", error);
    throw error;
  }
}

/**
 * Send welcome email to new users
 */
export async function sendWelcomeEmail(email: string, name: string): Promise<void> {
  const subject = "Welcome to Alumni Portal!";
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Alumni Portal</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Alumni Portal!</h1>
        </div>
        <div class="content">
          <h2>Hi ${name},</h2>
          <p>Thank you for registering with the Alumni Portal! Your account has been created and is pending approval.</p>
          <p>Once approved by an administrator, you'll be able to:</p>
          <ul>
            <li>Connect with fellow alumni and students</li>
            <li>Browse and apply for job opportunities</li>
            <li>Attend networking events</li>
            <li>Share advice and insights</li>
            <li>Stay updated with the latest news</li>
          </ul>
          <p>We'll notify you once your account is approved.</p>
          <p>
            <a href="${process.env.FRONTEND_URL}/login" class="button">
              Visit Alumni Portal
            </a>
          </p>
        </div>
        <div class="footer">
          <p>If you have any questions, please contact our support team.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  await sendEmail({ to: email, subject, html });
}

/**
 * Send account approval email
 */
export async function sendApprovalEmail(email: string, name: string): Promise<void> {
  const subject = "Your Alumni Portal account has been approved!";
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Account Approved</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Account Approved! 🎉</h1>
        </div>
        <div class="content">
          <h2>Hi ${name},</h2>
          <p>Great news! Your Alumni Portal account has been approved and is now active.</p>
          <p>You can now:</p>
          <ul>
            <li>Complete your profile</li>
            <li>Connect with other members</li>
            <li>Browse job opportunities</li>
            <li>Join upcoming events</li>
            <li>Share your experiences</li>
          </ul>
          <p>
            <a href="${process.env.FRONTEND_URL}/login" class="button">
              Login to Alumni Portal
            </a>
          </p>
        </div>
        <div class="footer">
          <p>Welcome to the Alumni Portal community!</p>
        </div>
      </div>
    </body>
    </html>
  `;

  await sendEmail({ to: email, subject, html });
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(email: string, name: string, resetToken: string): Promise<void> {
  const subject = "Reset your Alumni Portal password";
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Password Reset</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        .button { display: inline-block; padding: 10px 20px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset Request</h1>
        </div>
        <div class="content">
          <h2>Hi ${name},</h2>
          <p>You requested to reset your password for your Alumni Portal account.</p>
          <p>Click the button below to reset your password:</p>
          <p>
            <a href="${resetUrl}" class="button">
              Reset Password
            </a>
          </p>
          <p>If you didn't request this password reset, please ignore this email.</p>
          <p><strong>This link will expire in 1 hour.</strong></p>
        </div>
        <div class="footer">
          <p>If you're having trouble clicking the button, copy and paste this URL into your browser:</p>
          <p>${resetUrl}</p>
        </div>
      </div>
    </body>
    </html>
  `;

  await sendEmail({ to: email, subject, html });
}

/**
 * Strip HTML tags from text (simple implementation)
 */
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, "")
    .replace(/\s+/g, " ")
    .trim();
}

/**
 * Verify email configuration
 */
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log("✅ Email configuration verified");
    return true;
  } catch (error) {
    console.error("❌ Email configuration failed:", error);
    return false;
  }
}
