import rateLimit from "express-rate-limit";
import { rateLimitCache } from "../config/cache";
import { Logger } from "../services/loggerService";

// Custom memory store using node-cache for better performance
class NodeCacheStore {
  private cache = rateLimitCache;

  async increment(key: string): Promise<{ totalHits: number; timeToExpire?: number; resetTime: Date }> {
    const current = this.cache.get<number>(key) || 0;
    const totalHits = current + 1;

    // Set with TTL if it's a new key
    if (current === 0) {
      const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000");
      this.cache.set(key, totalHits, Math.ceil(windowMs / 1000));
      const resetTime = new Date(Date.now() + windowMs);
      return { totalHits, timeToExpire: windowMs, resetTime };
    } else {
      this.cache.set(key, totalHits);
      const ttl = this.cache.getTtl(key);
      const timeToExpire = ttl ? Math.max(0, ttl - Date.now()) : 0;
      const resetTime = ttl ? new Date(ttl) : new Date(Date.now() + 900000); // Default 15 min
      return { totalHits, timeToExpire, resetTime };
    }
  }

  async decrement(key: string): Promise<void> {
    const current = this.cache.get<number>(key) || 0;
    if (current > 0) {
      this.cache.set(key, current - 1);
    }
  }

  async resetKey(key: string): Promise<void> {
    this.cache.del(key);
  }
}

// Create optimized memory store instance
const memoryStore = new NodeCacheStore();

// General rate limiter with optimized memory store
export const rateLimiter = rateLimit({
  store: memoryStore,
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"), // limit each IP to 100 requests per windowMs
  message: {
    error: "Too many requests from this IP, please try again later.",
    retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000") / 1000),
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req, res) => {
    Logger.security("Rate limit exceeded", req.ip, {
      userAgent: req.get("User-Agent"),
      endpoint: req.path,
      method: req.method,
    });

    res.status(429).json({
      error: "Too many requests from this IP, please try again later.",
      retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000") / 1000),
    });
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === "/health";
  },
});

// Stricter rate limiting for auth endpoints
export const authRateLimiter = rateLimit({
  store: new NodeCacheStore(), // Separate store for auth
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs for auth
  message: {
    error: "Too many authentication attempts, please try again later.",
    retryAfter: 900, // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (_req, res) => {
    res.status(429).json({
      error: "Too many authentication attempts, please try again later.",
      retryAfter: 900,
    });
  },
});

// Log rate limiter initialization
Logger.info("Rate limiters initialized with optimized memory store", {
  generalWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"),
  generalMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"),
  authWindowMs: 15 * 60 * 1000,
  authMaxRequests: 5,
});
