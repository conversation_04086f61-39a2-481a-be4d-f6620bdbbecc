import { cache, CacheService } from "../config/cache";
import { Logger } from "../services/loggerService";

// Cache configuration is now handled in cache.ts

// Optimized single-level cache implementation using node-cache
export class AdvancedCacheService {
  private static cacheStats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    sets: 0,
    deletes: 0,
  };

  // Initialize cache service
  static init() {
    Logger.info("Advanced cache service initialized", {
      maxKeys: cache.options.maxKeys,
      stdTTL: cache.options.stdTTL,
      checkPeriod: cache.options.checkperiod,
    });

    // Set up cache event listeners for statistics
    cache.on("hit", () => {
      this.cacheStats.hits++;
    });

    cache.on("miss", () => {
      this.cacheStats.misses++;
    });

    cache.on("set", () => {
      this.cacheStats.sets++;
    });

    cache.on("del", () => {
      this.cacheStats.deletes++;
    });
  }

  // Get data (optimized synchronous operation)
  static get<T>(key: string): T | null {
    this.cacheStats.totalRequests++;

    const result = CacheService.get<T>(key);
    if (result !== null) {
      Logger.cache(`Cache hit for key: ${key}`);
      return result;
    }

    Logger.cache(`Cache miss for key: ${key}`);
    return null;
  }

  // Async version for compatibility
  static async getAsync<T>(key: string): Promise<T | null> {
    return this.get<T>(key);
  }

  // Set data (optimized synchronous operation)
  static set<T>(key: string, data: T, ttl: number = 3600): boolean {
    try {
      const success = CacheService.set(key, data, ttl);

      Logger.cache(`Cache set for key: ${key}`, key, {
        ttl,
        success,
      });

      return success;
    } catch (error) {
      Logger.error("Cache set error", { key, error });
      return false;
    }
  }

  // Async version for compatibility
  static async setAsync<T>(key: string, data: T, ttl: number = 3600): Promise<boolean> {
    return this.set(key, data, ttl);
  }

  // Delete from cache
  static delete(key: string): boolean {
    try {
      const success = CacheService.del(key);
      Logger.cache(`Cache delete for key: ${key}`, key, { success });
      return success;
    } catch (error) {
      Logger.error("Cache delete error", { key, error });
      return false;
    }
  }

  // Async version for compatibility
  static async deleteAsync(key: string): Promise<boolean> {
    return this.delete(key);
  }

  // Get or set pattern (optimized)
  static async getOrSet<T>(key: string, fetchFunction: () => Promise<T>, ttl: number = 3600): Promise<T | null> {
    // Try to get from cache first (synchronous for speed)
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch data
    try {
      const data = await fetchFunction();
      if (data !== null && data !== undefined) {
        this.set(key, data, ttl);
      }
      return data;
    } catch (error) {
      Logger.error("Cache getOrSet fetch error", { key, error });
      return null;
    }
  }

  // Batch operations (optimized for node-cache)
  static mget<T>(keys: string[]): Map<string, T | null> {
    const results = new Map<string, T | null>();

    try {
      for (const key of keys) {
        const value = this.get<T>(key);
        results.set(key, value);
      }
    } catch (error) {
      Logger.error("Cache mget error", { keys, error });
      // Set null for all keys on error
      for (const key of keys) {
        results.set(key, null);
      }
    }

    this.cacheStats.totalRequests += keys.length;
    return results;
  }

  // Async version for compatibility
  static async mgetAsync<T>(keys: string[]): Promise<Map<string, T | null>> {
    return this.mget<T>(keys);
  }

  // Cache warming - preload frequently accessed data (optimized)
  static async warmCache(
    warmingConfig: Array<{
      key: string;
      fetchFunction: () => Promise<any>;
      ttl?: number;
    }>
  ): Promise<void> {
    Logger.info(`Starting cache warming for ${warmingConfig.length} keys`);

    const startTime = Date.now();
    const results = await Promise.allSettled(
      warmingConfig.map(async ({ key, fetchFunction, ttl = 3600 }) => {
        try {
          const data = await fetchFunction();
          this.set(key, data, ttl); // Synchronous for better performance
          return { key, success: true };
        } catch (error) {
          Logger.error(`Cache warming failed for key: ${key}`, error);
          return { key, success: false, error };
        }
      })
    );

    const successful = results.filter((r) => r.status === "fulfilled" && r.value.success).length;
    const duration = Date.now() - startTime;

    Logger.performance("Cache warming completed", duration, {
      total: warmingConfig.length,
      successful,
      failed: warmingConfig.length - successful,
    });
  }

  // Cache invalidation patterns (optimized for node-cache)
  static invalidatePattern(pattern: string): number {
    try {
      const invalidated = CacheService.delPattern(pattern);

      Logger.cache(`Cache pattern invalidation: ${pattern}`, pattern, {
        invalidated,
      });

      return invalidated;
    } catch (error) {
      Logger.error("Cache pattern invalidation error", { pattern, error });
      return 0;
    }
  }

  // Async version for compatibility
  static async invalidatePatternAsync(pattern: string): Promise<number> {
    return this.invalidatePattern(pattern);
  }

  // Batch set operations
  static mset(entries: Array<{ key: string; data: any; ttl?: number }>): boolean {
    try {
      return CacheService.mset(entries);
    } catch (error) {
      Logger.error("Cache mset error", { error });
      return false;
    }
  }

  // Async version for compatibility
  static async msetAsync(entries: Array<{ key: string; data: any; ttl?: number }>): Promise<boolean> {
    return this.mset(entries);
  }

  // Get cache statistics (optimized for node-cache)
  static getStats() {
    const cacheStats = CacheService.getStats();
    const hitRate =
      this.cacheStats.totalRequests > 0
        ? ((this.cacheStats.hits / this.cacheStats.totalRequests) * 100).toFixed(2)
        : "0.00";

    return {
      cache: {
        size: cacheStats.keys,
        maxKeys: cache.options.maxKeys,
        hitRate: `${hitRate}%`,
        hits: this.cacheStats.hits,
        misses: this.cacheStats.misses,
        sets: this.cacheStats.sets,
        deletes: this.cacheStats.deletes,
        totalRequests: this.cacheStats.totalRequests,
      },
      nodeCache: cacheStats, // Raw node-cache stats
      timestamp: new Date().toISOString(),
    };
  }

  // Reset cache statistics
  static resetStats(): void {
    this.cacheStats = {
      hits: 0,
      misses: 0,
      totalRequests: 0,
      sets: 0,
      deletes: 0,
    };
    Logger.info("Cache statistics reset");
  }

  // Clear all caches
  static clearAll(): boolean {
    try {
      const success = CacheService.clear();
      this.resetStats();
      Logger.info("All caches cleared");
      return success;
    } catch (error) {
      Logger.error("Error clearing caches", { error });
      return false;
    }
  }

  // Async version for compatibility
  static async clearAllAsync(): Promise<boolean> {
    return this.clearAll();
  }
}

// Initialize the cache service
AdvancedCacheService.init();
