import { WebSocketServer, WebSocket } from "ws";
import { AuthUtils } from "../utils/auth";
import { prisma } from "../config/database";

interface ExtendedWebSocket extends WebSocket {
  userId?: string;
  userRole?: string;
  isAuthenticated?: boolean;
}

// Store active connections
const activeConnections = new Map<string, ExtendedWebSocket>();

export const setupWebSocket = (wss: WebSocketServer) => {
  console.log("Setting up WebSocket server...");

  wss.on("connection", (ws: ExtendedWebSocket, req) => {
    console.log("New WebSocket connection established");

    // Handle authentication and user identification
    ws.on("message", async (message: string) => {
      try {
        const data = JSON.parse(message);

        switch (data.type) {
          case "auth":
            await handleAuthentication(ws, data);
            break;

          case "send_message":
            await handleSendMessage(ws, data);
            break;

          case "join_conversation":
            await handleJoinConversation(ws, data);
            break;

          case "ping":
            ws.send(
              JSON.stringify({
                type: "pong",
                timestamp: Date.now(),
              })
            );
            break;

          default:
            ws.send(
              JSON.stringify({
                type: "error",
                message: "Unknown message type",
              })
            );
        }
      } catch (error) {
        console.error("WebSocket message error:", error);
        ws.send(
          JSON.stringify({
            type: "error",
            message: "Invalid message format",
          })
        );
      }
    });

    ws.on("close", () => {
      console.log("WebSocket connection closed");
      if (ws.userId) {
        activeConnections.delete(ws.userId);
      }
    });

    ws.on("error", (error) => {
      console.error("WebSocket error:", error);
    });

    // Send welcome message
    ws.send(
      JSON.stringify({
        type: "welcome",
        message: "Connected to Alumni Portal WebSocket server",
      })
    );
  });

  return wss;
};

/**
 * Handle WebSocket authentication
 */
async function handleAuthentication(ws: ExtendedWebSocket, data: any) {
  try {
    if (!data.token) {
      ws.send(
        JSON.stringify({
          type: "auth_error",
          message: "Token is required",
        })
      );
      return;
    }

    // Verify JWT token
    const payload = AuthUtils.verifyAccessToken(data.token);

    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        role: true,
        status: true,
        name: true,
      },
    });

    if (!user || user.status !== "APPROVED") {
      ws.send(
        JSON.stringify({
          type: "auth_error",
          message: "Invalid or expired token",
        })
      );
      return;
    }

    // Set user info on WebSocket
    ws.userId = user.id;
    ws.userRole = user.role;
    ws.isAuthenticated = true;

    // Store connection
    activeConnections.set(user.id, ws);

    ws.send(
      JSON.stringify({
        type: "auth_success",
        message: "Authentication successful",
        user: {
          id: user.id,
          name: user.name,
          role: user.role,
        },
      })
    );
  } catch (error) {
    ws.send(
      JSON.stringify({
        type: "auth_error",
        message: "Authentication failed",
      })
    );
  }
}

/**
 * Handle sending real-time messages
 */
async function handleSendMessage(ws: ExtendedWebSocket, data: any) {
  if (!ws.isAuthenticated || !ws.userId) {
    ws.send(
      JSON.stringify({
        type: "error",
        message: "Authentication required",
      })
    );
    return;
  }

  try {
    const { receiverId, content, messageId } = data;

    // Get receiver's WebSocket connection
    const receiverWs = activeConnections.get(receiverId);

    if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
      // Send real-time message to receiver
      receiverWs.send(
        JSON.stringify({
          type: "new_message",
          data: {
            id: messageId,
            senderId: ws.userId,
            content,
            timestamp: new Date().toISOString(),
          },
        })
      );
    }

    // Confirm message sent to sender
    ws.send(
      JSON.stringify({
        type: "message_sent",
        messageId,
      })
    );
  } catch (error) {
    ws.send(
      JSON.stringify({
        type: "error",
        message: "Failed to send message",
      })
    );
  }
}

/**
 * Handle joining a conversation (for typing indicators, etc.)
 */
async function handleJoinConversation(ws: ExtendedWebSocket, data: any) {
  if (!ws.isAuthenticated || !ws.userId) {
    ws.send(
      JSON.stringify({
        type: "error",
        message: "Authentication required",
      })
    );
    return;
  }

  try {
    const { conversationUserId } = data;

    // Verify connection exists between users
    const connection = await prisma.connection.findFirst({
      where: {
        OR: [
          { requesterId: ws.userId, receiverId: conversationUserId, status: "ACCEPTED" },
          { requesterId: conversationUserId, receiverId: ws.userId, status: "ACCEPTED" },
        ],
      },
    });

    if (!connection) {
      ws.send(
        JSON.stringify({
          type: "error",
          message: "Not authorized to join this conversation",
        })
      );
      return;
    }

    ws.send(
      JSON.stringify({
        type: "conversation_joined",
        conversationUserId,
      })
    );
  } catch (error) {
    ws.send(
      JSON.stringify({
        type: "error",
        message: "Failed to join conversation",
      })
    );
  }
}

/**
 * Send real-time message to user (called from message controller)
 */
export function sendRealTimeMessage(receiverId: string, messageData: any) {
  const receiverWs = activeConnections.get(receiverId);

  if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
    receiverWs.send(
      JSON.stringify({
        type: "new_message",
        data: messageData,
      })
    );
    return true;
  }

  return false;
}

/**
 * Send notification to user (called from notification system)
 */
export function sendRealTimeNotification(userId: string, notificationData: any) {
  const userWs = activeConnections.get(userId);

  if (userWs && userWs.readyState === WebSocket.OPEN) {
    userWs.send(
      JSON.stringify({
        type: "new_notification",
        data: notificationData,
      })
    );
    return true;
  }

  return false;
}
