// Ultra-fast JSON serialization utilities for 50ms response times

// Fast JSON stringifier for simple objects
export class FastSerializer {
  // Cache for frequently serialized objects
  private static serializationCache = new Map<string, string>();
  private static maxCacheSize = 1000;

  // Ultra-fast JSON stringify for simple objects
  static stringify(obj: any): string {
    // Handle null/undefined
    if (obj === null) return "null";
    if (obj === undefined) return "undefined";

    // Handle primitives
    if (typeof obj === "string") return `"${obj.replace(/"/g, '\\"')}"`;
    if (typeof obj === "number") return obj.toString();
    if (typeof obj === "boolean") return obj.toString();

    // Handle arrays (simple case)
    if (Array.isArray(obj)) {
      if (obj.length === 0) return "[]";
      if (obj.length < 10 && obj.every((item) => typeof item === "string" || typeof item === "number")) {
        return `[${obj.map((item) => (typeof item === "string" ? `"${item}"` : item)).join(",")}]`;
      }
    }

    // Handle simple objects (most common case)
    if (typeof obj === "object" && obj.constructor === Object) {
      const keys = Object.keys(obj);

      // Very simple objects - inline serialization
      if (
        keys.length <= 5 &&
        keys.every(
          (key) => typeof obj[key] === "string" || typeof obj[key] === "number" || typeof obj[key] === "boolean"
        )
      ) {
        const pairs = keys.map((key) => {
          const value = obj[key];
          const serializedValue = typeof value === "string" ? `"${value.replace(/"/g, '\\"')}"` : value;
          return `"${key}":${serializedValue}`;
        });
        return `{${pairs.join(",")}}`;
      }
    }

    // Fall back to native JSON.stringify for complex objects
    return JSON.stringify(obj);
  }

  // Cached serialization for frequently accessed objects
  static cachedStringify(obj: any, cacheKey?: string): string {
    if (!cacheKey) {
      return this.stringify(obj);
    }

    // Check cache first
    const cached = this.serializationCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // Serialize and cache
    const serialized = this.stringify(obj);

    // Manage cache size
    if (this.serializationCache.size >= this.maxCacheSize) {
      // Remove oldest entries (simple FIFO)
      const firstKey = this.serializationCache.keys().next().value;
      if (firstKey) {
        this.serializationCache.delete(firstKey);
      }
    }

    this.serializationCache.set(cacheKey, serialized);
    return serialized;
  }

  // Clear serialization cache
  static clearCache(): void {
    this.serializationCache.clear();
  }

  // Get cache statistics
  static getCacheStats() {
    return {
      size: this.serializationCache.size,
      maxSize: this.maxCacheSize,
      hitRate: "N/A", // Would need hit tracking for this
    };
  }
}

// Fast response builders for common API patterns
export class FastResponseBuilder {
  // Build success response
  static success(data: any, message?: string): string {
    if (!message) {
      return FastSerializer.stringify({
        success: true,
        data,
        timestamp: new Date().toISOString(),
      });
    }

    return FastSerializer.stringify({
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
    });
  }

  // Build error response
  static error(message: string, code?: string, details?: any): string {
    const response: any = {
      success: false,
      error: message,
      timestamp: new Date().toISOString(),
    };

    if (code) response.code = code;
    if (details) response.details = details;

    return FastSerializer.stringify(response);
  }

  // Build paginated response
  static paginated(data: any[], page: number, limit: number, total: number): string {
    return FastSerializer.stringify({
      success: true,
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  }

  // Build list response (optimized for simple arrays)
  static list(items: any[], total?: number): string {
    const response: any = {
      success: true,
      data: items,
      count: items.length,
      timestamp: new Date().toISOString(),
    };

    if (total !== undefined) {
      response.total = total;
    }

    return FastSerializer.stringify(response);
  }

  // Build health check response
  static health(status: string, details?: any): string {
    const response: any = {
      status,
      timestamp: new Date().toISOString(),
    };

    if (details) {
      response.details = details;
    }

    return FastSerializer.stringify(response);
  }
}

// Optimized response middleware
export const fastJsonResponse = () => {
  return (req: any, res: any, next: any) => {
    // Override res.json for faster serialization
    const originalJson = res.json.bind(res);

    res.json = function (data: any) {
      // Set content type
      res.set("Content-Type", "application/json");

      // Use fast serialization for simple responses
      try {
        const serialized = FastSerializer.stringify(data);
        return res.send(serialized);
      } catch (error) {
        // Fall back to original method if fast serialization fails
        return originalJson(data);
      }
    };

    // Add helper methods
    res.success = function (data: any, message?: string) {
      const response = FastResponseBuilder.success(data, message);
      res.set("Content-Type", "application/json");
      return res.send(response);
    };

    res.error = function (message: string, code?: string, details?: any) {
      const response = FastResponseBuilder.error(message, code, details);
      res.set("Content-Type", "application/json");
      return res.status(400).send(response);
    };

    res.paginated = function (data: any[], page: number, limit: number, total: number) {
      const response = FastResponseBuilder.paginated(data, page, limit, total);
      res.set("Content-Type", "application/json");
      return res.send(response);
    };

    next();
  };
};

// Memory management - clear caches periodically
setInterval(() => {
  FastSerializer.clearCache();
}, 10 * 60 * 1000); // Clear every 10 minutes
