### Login to get access token
POST http://localhost:5000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}

### Test authenticated endpoint (replace YOUR_ACCESS_TOKEN with the token from login response)
GET http://localhost:5000/api/users/profile
Authorization: Bearer YOUR_ACCESS_TOKEN

### Test user directory
GET http://localhost:5000/api/users/directory
Authorization: Bearer YOUR_ACCESS_TOKEN

### Test health endpoint (no auth required)
GET http://localhost:5000/health
