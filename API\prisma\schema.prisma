generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                      String                   @id @default(cuid())
  email                   String                   @unique
  password                String
  name                    String
  mobile                  String?
  usn                     String                   @unique
  course                  String
  batch                   String
  role                    UserRole                 @default(STUDENT)
  status                  UserStatus               @default(PENDING)
  profilePicture          String?
  bio                     String?
  linkedinUrl             String?
  githubUrl               String?
  portfolioUrl            String?
  company                 String?
  jobTitle                String?
  experience              Int?
  location                String?
  showEmail               Boolean                  @default(false)
  showMobile              Boolean                  @default(false)
  showLinkedin            Boolean                  @default(true)
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  lastLoginAt             DateTime?
  receivedConnections     Connection[]             @relation("ConnectionReceiver")
  connections             Connection[]             @relation("ConnectionRequester")
  receivedMessages        Message[]                @relation("MessageReceiver")
  sentMessages            Message[]                @relation("MessageSender")
  notificationPreferences NotificationPreferences?
  notifications           Notification[]
  posts                   Post[]

  @@index([email])
  @@index([status])
  @@index([role])
  @@index([course])
  @@index([batch])
  @@index([createdAt])
  @@index([status, role])
  @@index([course, batch])
  @@map("users")
}

model Post {
  id        String   @id @default(cuid())
  title     String
  content   String
  type      PostType @default(GENERAL)
  isPublic  Boolean  @default(true)
  imageUrl  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@index([authorId])
  @@index([type])
  @@index([isPublic])
  @@index([createdAt])
  @@index([isPublic, type])
  @@index([isPublic, createdAt])
  @@index([authorId, createdAt])
  @@map("posts")
}

model Message {
  id         String   @id @default(cuid())
  content    String
  isRead     Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  senderId   String
  receiverId String
  receiver   User     @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  sender     User     @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)

  @@index([senderId])
  @@index([receiverId])
  @@index([isRead])
  @@index([createdAt])
  @@index([senderId, receiverId])
  @@index([receiverId, isRead])
  @@index([senderId, createdAt])
  @@index([receiverId, createdAt])
  @@map("messages")
}

model Connection {
  id          String   @id @default(cuid())
  status      String   @default("PENDING")
  message     String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  requesterId String
  receiverId  String
  receiver    User     @relation("ConnectionReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  requester   User     @relation("ConnectionRequester", fields: [requesterId], references: [id], onDelete: Cascade)

  @@unique([requesterId, receiverId])
  @@index([requesterId])
  @@index([receiverId])
  @@index([status])
  @@index([createdAt])
  @@index([requesterId, status])
  @@index([receiverId, status])
  @@map("connections")
}

model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  data      Json?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  userId    String
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
  @@index([userId, isRead])
  @@index([userId, type])
  @@index([userId, createdAt])
  @@map("notifications")
}

model NotificationPreferences {
  id                     String   @id @default(cuid())
  emailMessageReceived   Boolean  @default(true)
  emailConnectionRequest Boolean  @default(true)
  emailPostCreated       Boolean  @default(false)
  emailSystemUpdates     Boolean  @default(true)
  inAppMessageReceived   Boolean  @default(true)
  inAppConnectionRequest Boolean  @default(true)
  inAppPostCreated       Boolean  @default(true)
  inAppSystemUpdates     Boolean  @default(true)
  emailDigest            Boolean  @default(false)
  emailDigestFrequency   String   @default("WEEKLY")
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  userId                 String   @unique
  user                   User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_preferences")
}

enum UserRole {
  STUDENT
  ALUMNI
  ADMIN
}

enum UserStatus {
  PENDING
  APPROVED
  REJECTED
  SUSPENDED
}

enum PostType {
  ADVICE
  GENERAL
  ANNOUNCEMENT
}

enum NotificationType {
  MESSAGE_RECEIVED
  CONNECTION_REQUEST
  POST_CREATED
  SYSTEM
}
