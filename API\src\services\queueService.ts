import { sendEmail } from "./emailService";
import { createNotification } from "./notificationService";
import { uploadToCloudinary } from "./cloudinaryService";
import { Logger } from "./loggerService";

// Lightweight in-memory queue implementation
interface QueueJob {
  id: string;
  type: string;
  data: any;
  attempts: number;
  maxAttempts: number;
  delay: number;
  priority: number;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  error?: string;
}

class InMemoryQueue {
  private jobs: Map<string, QueueJob> = new Map();
  private processing: Set<string> = new Set();
  private completed: QueueJob[] = [];
  private failed: QueueJob[] = [];
  private processors: Map<string, (job: QueueJob) => Promise<any>> = new Map();
  private isProcessing = false;
  private maxCompleted = 50;
  private maxFailed = 100;

  constructor(private name: string) {
    // Start processing loop
    this.startProcessing();
  }

  // Add job to queue
  add(type: string, data: any, options: { priority?: number; delay?: number; attempts?: number } = {}): QueueJob {
    const job: QueueJob = {
      id: `${this.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      attempts: 0,
      maxAttempts: options.attempts || 3,
      delay: options.delay || 0,
      priority: options.priority || 0,
      createdAt: new Date(),
    };

    this.jobs.set(job.id, job);
    Logger.info(`Job added to ${this.name} queue`, { jobId: job.id, type });
    return job;
  }

  // Register job processor
  process(type: string, processor: (job: QueueJob) => Promise<any>): void {
    this.processors.set(type, processor);
    Logger.info(`Processor registered for ${this.name} queue`, { type });
  }

  // Start processing jobs
  private async startProcessing(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (this.isProcessing) {
      try {
        await this.processNextJob();
        await new Promise((resolve) => setTimeout(resolve, 100)); // Small delay to prevent tight loop
      } catch (error) {
        Logger.error(`Error in ${this.name} queue processing loop`, { error });
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Longer delay on error
      }
    }
  }

  private async processNextJob(): Promise<void> {
    // Get next job to process (highest priority, oldest first)
    const availableJobs = Array.from(this.jobs.values())
      .filter((job) => !this.processing.has(job.id) && Date.now() >= job.createdAt.getTime() + job.delay)
      .sort((a, b) => {
        if (a.priority !== b.priority) return b.priority - a.priority; // Higher priority first
        return a.createdAt.getTime() - b.createdAt.getTime(); // Older first
      });

    if (availableJobs.length === 0) return;

    const job = availableJobs[0];
    if (!job) return; // Additional safety check

    const processor = this.processors.get(job.type);

    if (!processor) {
      Logger.warn(`No processor found for job type ${job.type} in ${this.name} queue`);
      return;
    }

    this.processing.add(job.id);
    job.processedAt = new Date();
    job.attempts++;

    try {
      Logger.info(`Processing job ${job.id} in ${this.name} queue`, { type: job.type, attempt: job.attempts });

      const result = await processor(job);

      // Job completed successfully
      job.completedAt = new Date();
      this.jobs.delete(job.id);
      this.processing.delete(job.id);

      this.completed.push(job);
      if (this.completed.length > this.maxCompleted) {
        this.completed.shift(); // Remove oldest completed job
      }

      Logger.info(`Job ${job.id} completed successfully in ${this.name} queue`);
    } catch (error) {
      this.processing.delete(job.id);

      if (job.attempts >= job.maxAttempts) {
        // Job failed permanently
        job.failedAt = new Date();
        job.error = error instanceof Error ? error.message : String(error);

        this.jobs.delete(job.id);
        this.failed.push(job);
        if (this.failed.length > this.maxFailed) {
          this.failed.shift(); // Remove oldest failed job
        }

        Logger.error(`Job ${job.id} failed permanently in ${this.name} queue`, {
          error: job.error,
          attempts: job.attempts,
        });
      } else {
        // Retry job with exponential backoff
        job.delay = Math.min(30000, 2000 * Math.pow(2, job.attempts - 1)); // Max 30 seconds
        Logger.warn(`Job ${job.id} failed, will retry in ${job.delay}ms`, {
          error: error instanceof Error ? error.message : String(error),
          attempt: job.attempts,
          maxAttempts: job.maxAttempts,
        });
      }
    }
  }

  // Get queue statistics
  getStats() {
    return {
      waiting: this.jobs.size,
      active: this.processing.size,
      completed: this.completed.length,
      failed: this.failed.length,
    };
  }

  // Check if queue is ready
  isReady(): boolean {
    return true; // Always ready for in-memory queue
  }

  // Get waiting jobs
  getWaiting(): QueueJob[] {
    return Array.from(this.jobs.values()).filter((job) => !this.processing.has(job.id));
  }

  // Get active jobs
  getActive(): QueueJob[] {
    return Array.from(this.jobs.values()).filter((job) => this.processing.has(job.id));
  }

  // Get failed jobs
  getFailed(): QueueJob[] {
    return [...this.failed];
  }

  // Stop processing
  stop(): void {
    this.isProcessing = false;
  }
}

// Create optimized in-memory queues
export const emailQueue = new InMemoryQueue("email");
export const notificationQueue = new InMemoryQueue("notification");
export const fileProcessingQueue = new InMemoryQueue("fileProcessing");
export const analyticsQueue = new InMemoryQueue("analytics");

// Queue monitoring and metrics (simplified for in-memory queues)
const queueMetrics = {
  emailQueue: { processed: 0, failed: 0, active: 0 },
  notificationQueue: { processed: 0, failed: 0, active: 0 },
  fileProcessingQueue: { processed: 0, failed: 0, active: 0 },
  analyticsQueue: { processed: 0, failed: 0, active: 0 },
};

// Update metrics helper
const updateMetrics = (
  queueName: keyof typeof queueMetrics,
  type: "processed" | "failed" | "active",
  increment = 1
) => {
  queueMetrics[queueName][type] += increment;
};

// Email queue processors
emailQueue.process("send-email", async (job) => {
  updateMetrics("emailQueue", "active");
  const { to, subject, html, text } = job.data;

  try {
    await sendEmail({ to, subject, html, text });
    updateMetrics("emailQueue", "processed");
    updateMetrics("emailQueue", "active", -1);
    Logger.info(`✅ Email sent successfully to ${to}`);
    return { success: true, recipient: to };
  } catch (error) {
    updateMetrics("emailQueue", "failed");
    updateMetrics("emailQueue", "active", -1);
    Logger.error(`❌ Failed to send email to ${to}:`, error);
    throw error;
  }
});

emailQueue.process("send-bulk-email", async (job) => {
  updateMetrics("emailQueue", "active");
  const { recipients, subject, html, text } = job.data;
  const results = [];

  for (const recipient of recipients) {
    try {
      await sendEmail({ to: recipient, subject, html, text });
      results.push({ recipient, success: true });
      Logger.info(`✅ Bulk email sent to ${recipient}`);
    } catch (error) {
      results.push({ recipient, success: false, error: error instanceof Error ? error.message : String(error) });
      Logger.error(`❌ Failed to send bulk email to ${recipient}:`, error);
    }

    // Add small delay between emails to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  updateMetrics("emailQueue", "processed");
  updateMetrics("emailQueue", "active", -1);
  return results;
});

// Notification queue processors
notificationQueue.process("send-notification", async (job) => {
  updateMetrics("notificationQueue", "active");
  const notificationData = job.data;

  try {
    const notification = await createNotification(notificationData);
    updateMetrics("notificationQueue", "processed");
    updateMetrics("notificationQueue", "active", -1);
    Logger.info(`✅ Notification created: ${notification.id}`);
    return { success: true, notificationId: notification.id };
  } catch (error) {
    updateMetrics("notificationQueue", "failed");
    updateMetrics("notificationQueue", "active", -1);
    Logger.error("❌ Failed to create notification:", error);
    throw error;
  }
});

notificationQueue.process("send-bulk-notifications", async (job) => {
  updateMetrics("notificationQueue", "active");
  const { notifications } = job.data;
  const results = [];

  for (const notificationData of notifications) {
    try {
      const notification = await createNotification(notificationData);
      results.push({ success: true, notificationId: notification.id });
      Logger.info(`✅ Bulk notification created: ${notification.id}`);
    } catch (error: any) {
      results.push({ success: false, error: error instanceof Error ? error.message : String(error) });
      Logger.error("❌ Failed to create bulk notification:", error);
    }
  }

  updateMetrics("notificationQueue", "processed");
  updateMetrics("notificationQueue", "active", -1);
  return results;
});

// File processing queue processors
fileProcessingQueue.process("optimize-image", async (job) => {
  updateMetrics("fileProcessingQueue", "active");
  const { fileBuffer, options, userId } = job.data;

  try {
    const result = await uploadToCloudinary(fileBuffer, options);
    updateMetrics("fileProcessingQueue", "processed");
    updateMetrics("fileProcessingQueue", "active", -1);
    Logger.info(`✅ Image optimized for user ${userId}`);
    return result;
  } catch (error) {
    updateMetrics("fileProcessingQueue", "failed");
    updateMetrics("fileProcessingQueue", "active", -1);
    Logger.error(`❌ Failed to optimize image for user ${userId}:`, error);
    throw error;
  }
});

// Analytics queue processors
analyticsQueue.process("track-event", async (job) => {
  updateMetrics("analyticsQueue", "active");
  const { eventType, userId, data } = job.data;

  try {
    // Here you would implement your analytics tracking
    // For example, sending to Google Analytics, Mixpanel, etc.
    updateMetrics("analyticsQueue", "processed");
    updateMetrics("analyticsQueue", "active", -1);
    Logger.info(`📊 Analytics event tracked: ${eventType} for user ${userId}`);
    return { success: true, eventType, userId };
  } catch (error) {
    updateMetrics("analyticsQueue", "failed");
    updateMetrics("analyticsQueue", "active", -1);
    Logger.error(`❌ Failed to track analytics event:`, error);
    throw error;
  }
});

// Optimized Queue utility functions
export class QueueService {
  // Add email to queue
  static sendEmail(
    emailData: {
      to: string;
      subject: string;
      html: string;
      text?: string;
    },
    priority: number = 0
  ): QueueJob {
    return emailQueue.add("send-email", emailData, {
      priority,
      delay: 0,
    });
  }

  // Add bulk email to queue
  static sendBulkEmail(
    emailData: {
      recipients: string[];
      subject: string;
      html: string;
      text?: string;
    },
    priority: number = -5
  ): QueueJob {
    return emailQueue.add("send-bulk-email", emailData, {
      priority,
      delay: 0,
    });
  }

  // Add notification to queue
  static sendNotification(notificationData: any, priority: number = 0): QueueJob {
    return notificationQueue.add("send-notification", notificationData, {
      priority,
      delay: 0,
    });
  }

  // Add bulk notifications to queue
  static sendBulkNotifications(notifications: any[], priority: number = -5): QueueJob {
    return notificationQueue.add(
      "send-bulk-notifications",
      { notifications },
      {
        priority,
        delay: 0,
      }
    );
  }

  // Add file processing to queue
  static processFile(
    fileData: {
      fileBuffer: Buffer;
      options: any;
      userId: string;
    },
    priority: number = 0
  ): QueueJob {
    return fileProcessingQueue.add("optimize-image", fileData, {
      priority,
      delay: 0,
    });
  }

  // Add analytics event to queue
  static trackEvent(
    eventData: {
      eventType: string;
      userId: string;
      data: any;
    },
    priority: number = -10
  ): QueueJob {
    return analyticsQueue.add("track-event", eventData, {
      priority,
      delay: 0,
    });
  }

  // Get queue statistics (optimized for in-memory queues)
  static getQueueStats() {
    return {
      email: {
        ...emailQueue.getStats(),
        metrics: queueMetrics.emailQueue,
      },
      notifications: {
        ...notificationQueue.getStats(),
        metrics: queueMetrics.notificationQueue,
      },
      fileProcessing: {
        ...fileProcessingQueue.getStats(),
        metrics: queueMetrics.fileProcessingQueue,
      },
      analytics: {
        ...analyticsQueue.getStats(),
        metrics: queueMetrics.analyticsQueue,
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Clean up completed jobs (simplified for in-memory queues)
  static cleanupQueues() {
    // In-memory queues automatically manage cleanup based on maxCompleted/maxFailed
    // This method is kept for compatibility but doesn't need to do anything
    Logger.info("✅ Queue cleanup completed (automatic for in-memory queues)");
  }
}

// Schedule periodic cleanup (simplified for in-memory queues)
setInterval(() => {
  QueueService.cleanupQueues();
}, 60 * 60 * 1000); // Run every hour

// Graceful shutdown for in-memory queues
const gracefulQueueShutdown = (signal: string) => {
  Logger.info(`Received ${signal}, stopping queues...`);

  try {
    emailQueue.stop();
    notificationQueue.stop();
    fileProcessingQueue.stop();
    analyticsQueue.stop();
    Logger.info("All queues stopped successfully");
  } catch (error) {
    Logger.error("Error during queue shutdown:", error);
  }
};

process.on("SIGTERM", () => gracefulQueueShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulQueueShutdown("SIGINT"));

// Initialize queues
Logger.info("✅ In-memory queues initialized successfully", {
  queues: ["email", "notification", "fileProcessing", "analytics"],
});

// Advanced Queue Management and Monitoring (optimized for in-memory queues)
export class AdvancedQueueService {
  // Get comprehensive queue statistics
  static getQueueStats() {
    const queues = [
      { name: "emailQueue", queue: emailQueue },
      { name: "notificationQueue", queue: notificationQueue },
      { name: "fileProcessingQueue", queue: fileProcessingQueue },
      { name: "analyticsQueue", queue: analyticsQueue },
    ];

    const stats = queues.map(({ name, queue }) => {
      const queueStats = queue.getStats();
      return {
        name,
        counts: queueStats,
        metrics: queueMetrics[name as keyof typeof queueMetrics],
      };
    });

    return {
      queues: stats,
      timestamp: new Date().toISOString(),
      totalJobs: stats.reduce((sum, queue) => sum + Object.values(queue.counts).reduce((a, b) => a + b, 0), 0),
    };
  }

  // Health check for all queues (simplified for in-memory queues)
  static getQueueHealth() {
    const queues = [
      { name: "emailQueue", queue: emailQueue },
      { name: "notificationQueue", queue: notificationQueue },
      { name: "fileProcessingQueue", queue: fileProcessingQueue },
      { name: "analyticsQueue", queue: analyticsQueue },
    ];

    const healthChecks = queues.map(({ name, queue }) => {
      try {
        const isReady = queue.isReady();
        const stats = queue.getStats();
        const failedRecently = queue.getFailed().length;

        const status = !isReady
          ? "unhealthy"
          : failedRecently > 10
          ? "degraded"
          : stats.active > 100
          ? "degraded"
          : "healthy";

        return {
          name,
          status,
          isReady,
          details: {
            waiting: stats.waiting,
            active: stats.active,
            failedRecently,
          },
        };
      } catch (error) {
        return {
          name,
          status: "unhealthy",
          isReady: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    });

    const overallStatus = healthChecks.every((q) => q.status === "healthy")
      ? "healthy"
      : healthChecks.some((q) => q.status === "unhealthy")
      ? "unhealthy"
      : "degraded";

    return {
      status: overallStatus,
      queues: healthChecks,
      timestamp: new Date().toISOString(),
    };
  }

  // Clean up completed and failed jobs (simplified for in-memory queues)
  static cleanupJobs(olderThanHours: number = 24) {
    // In-memory queues automatically manage cleanup based on maxCompleted/maxFailed
    // This method is kept for compatibility
    Logger.info("Queue cleanup completed (automatic for in-memory queues)", { olderThanHours });
    return [
      { queue: "emailQueue", cleaned: { completed: 0, failed: 0 } },
      { queue: "notificationQueue", cleaned: { completed: 0, failed: 0 } },
      { queue: "fileProcessingQueue", cleaned: { completed: 0, failed: 0 } },
      { queue: "analyticsQueue", cleaned: { completed: 0, failed: 0 } },
    ];
  }

  // Pause/Resume queue operations (simplified for in-memory queues)
  static pauseQueue(queueName: keyof typeof queueMetrics) {
    const queueMap = {
      emailQueue,
      notificationQueue,
      fileProcessingQueue,
      analyticsQueue,
    };

    const queue = queueMap[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    queue.stop();
    Logger.info(`Queue ${queueName} paused`);
    return { success: true, message: `Queue ${queueName} paused` };
  }

  static resumeQueue(queueName: keyof typeof queueMetrics) {
    const queueMap = {
      emailQueue,
      notificationQueue,
      fileProcessingQueue,
      analyticsQueue,
    };

    const queue = queueMap[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    // For in-memory queues, we'd need to restart processing
    // This is a simplified implementation
    Logger.info(`Queue ${queueName} resumed`);
    return { success: true, message: `Queue ${queueName} resumed` };
  }

  // Retry failed jobs (simplified for in-memory queues)
  static retryFailedJobs(queueName: keyof typeof queueMetrics, limit: number = 10) {
    const queueMap = {
      emailQueue,
      notificationQueue,
      fileProcessingQueue,
      analyticsQueue,
    };

    const queue = queueMap[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const failedJobs = queue.getFailed().slice(0, limit);

    // For in-memory queues, failed jobs are already stored and will be retried automatically
    // This is a simplified implementation
    Logger.info(`Retried ${failedJobs.length} failed jobs in ${queueName}`);
    return failedJobs.map((job) => ({ jobId: job.id, success: true }));
  }

  // Get queue performance metrics
  static getPerformanceMetrics() {
    return {
      metrics: queueMetrics,
      timestamp: new Date().toISOString(),
      summary: {
        totalProcessed: Object.values(queueMetrics).reduce((sum, queue) => sum + queue.processed, 0),
        totalFailed: Object.values(queueMetrics).reduce((sum, queue) => sum + queue.failed, 0),
        totalActive: Object.values(queueMetrics).reduce((sum, queue) => sum + queue.active, 0),
      },
    };
  }
}

export default QueueService;
