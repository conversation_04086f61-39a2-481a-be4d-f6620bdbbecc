import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { AuthUtils } from "../utils/auth";
import { createError } from "../middleware/errorHandler";

interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  mobile?: string;
  usn: string;
  course: string;
  batch: string;
  role: UserRole;
}

interface LoginRequest {
  email: string;
  password: string;
}

interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * Register a new user
 */
export const register = async (req: Request<{}, {}, RegisterRequest>, res: Response, next: NextFunction) => {
  try {
    const { email, password, name, mobile, usn, course, batch, role } = req.body;

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ email }, { usn }],
      },
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw createError("User with this email already exists", 409);
      }
      if (existingUser.usn === usn) {
        throw createError("User with this USN already exists", 409);
      }
    }

    // Hash password
    const hashedPassword = await AuthUtils.hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        mobile: mobile ?? null,
        usn,
        course,
        batch,
        role,
        status: UserStatus.PENDING, // Requires admin approval
      },
      select: {
        id: true,
        email: true,
        name: true,
        usn: true,
        course: true,
        batch: true,
        role: true,
        status: true,
        createdAt: true,
      },
    });

    res.status(201).json({
      message: "Registration successful. Your account is pending approval.",
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Login user
 */
export const login = async (req: Request<{}, {}, LoginRequest>, res: Response, next: NextFunction) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw createError("Invalid email or password", 401);
    }

    // Check password
    const isPasswordValid = await AuthUtils.comparePassword(password, user.password);
    if (!isPasswordValid) {
      throw createError("Invalid email or password", 401);
    }

    // Check user status
    if (user.status === UserStatus.REJECTED) {
      throw createError("Your account has been rejected. Please contact admin.", 403);
    }

    if (user.status === UserStatus.SUSPENDED) {
      throw createError("Your account has been suspended. Please contact admin.", 403);
    }

    if (user.status === UserStatus.PENDING) {
      throw createError("Your account is pending approval. Please wait for admin approval.", 403);
    }

    // Generate tokens
    const tokens = AuthUtils.generateTokenPair(user);

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Set refresh token as httpOnly cookie
    res.cookie("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    res.json({
      message: "Login successful",
      accessToken: tokens.accessToken,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        status: user.status,
        profilePicture: user.profilePicture,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Logout user
 */
export const logout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Clear refresh token cookie
    res.clearCookie("refreshToken");

    res.json({
      message: "Logout successful",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Refresh access token
 */
export const refreshToken = async (req: Request<{}, {}, RefreshTokenRequest>, res: Response, next: NextFunction) => {
  try {
    const { refreshToken } = req.body;

    // Also check cookie if not in body
    const token = refreshToken || req.cookies.refreshToken;

    if (!token) {
      throw createError("Refresh token is required", 401);
    }

    // Verify refresh token
    const payload = AuthUtils.verifyRefreshToken(token);

    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
    });

    if (!user) {
      throw createError("User not found", 401);
    }

    if (user.status !== UserStatus.APPROVED) {
      throw createError("Account is not approved", 403);
    }

    // Generate new tokens
    const tokens = AuthUtils.generateTokenPair(user);

    // Set new refresh token as httpOnly cookie
    res.cookie("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    res.json({
      message: "Token refreshed successfully",
      accessToken: tokens.accessToken,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current user
 */
export const getCurrentUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        email: true,
        name: true,
        mobile: true,
        usn: true,
        course: true,
        batch: true,
        role: true,
        status: true,
        profilePicture: true,
        bio: true,
        linkedinUrl: true,
        githubUrl: true,
        portfolioUrl: true,
        company: true,
        jobTitle: true,
        experience: true,
        location: true,
        showEmail: true,
        showMobile: true,
        showLinkedin: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    res.json({
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Verify email (placeholder for future implementation)
 */
export const verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    res.status(501).json({
      message: "Email verification feature coming soon",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Forgot password (placeholder for future implementation)
 */
export const forgotPassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    res.status(501).json({
      message: "Forgot password feature coming soon",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Reset password (placeholder for future implementation)
 */
export const resetPassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    res.status(501).json({
      message: "Reset password feature coming soon",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
