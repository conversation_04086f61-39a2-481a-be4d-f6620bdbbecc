# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/ionalumni"

# Server Configuration
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here
JWT_REFRESH_EXPIRES_IN=30d

# Bcrypt Configuration
BCRYPT_SALT_ROUNDS=12

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=IonAlumni

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Node-Cache Configuration (replaces Redis)
CACHE_DEFAULT_TTL=3600
CACHE_CHECK_PERIOD=600
CACHE_MAX_KEYS=10000

# Session Cache Configuration
SESSION_TTL=1800
SESSION_MAX_KEYS=5000

# Rate Limiting Cache Configuration
RATE_LIMIT_TTL=900
RATE_LIMIT_MAX_KEYS=10000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Queue Configuration (in-memory queues)
QUEUE_KEEP_COMPLETED=50
QUEUE_KEEP_FAILED=100
QUEUE_MAX_ATTEMPTS=3
QUEUE_BACKOFF_DELAY=2000

# Performance Configuration
ENABLE_CACHE_PRELOADING=true
ENABLE_FAST_SERIALIZATION=true
TARGET_RESPONSE_TIME=50

# Cloudinary Configuration (for file uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Monitoring Configuration
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_CACHE_MONITORING=true
ENABLE_QUEUE_MONITORING=true
